def analyze_feature_importance(df, shift_data, output_dir):
    """
    Use machine learning models to analyze feature importance and identify factors affecting DTRK parameter
    """
    print("Analyzing feature importance using machine learning models...")
    
    # Create ML analysis directory
    ml_dir = os.path.join(output_dir, 'ml_analysis')
    os.makedirs(ml_dir, exist_ok=True)
    
    # Select potential features
    potential_features = [
        'drive_part_num', 'drive_sbr_num', 'cms_config', 'stack_part_num',
        'hga_supplier', 'motor_vend_id', 'disc_install_id', 'tester_id',
        'interface', 'product', 'capacity', 'cs1_group'
    ]
    
    # Filter for features that exist in the data
    features = [col for col in potential_features if col in df.columns]
    
    if not features:
        print("Warning: No valid features found for ML analysis")
        return None, None
    
    # Prepare data for ML
    X = df[features].copy()
    y = df['p285pre2_dtrk'].copy()
    
    # Handle categorical features
    categorical_features = X.select_dtypes(include=['object']).columns.tolist()
    numerical_features = X.select_dtypes(exclude=['object']).columns.tolist()
    
    # Create preprocessor
    preprocessor = ColumnTransformer(
        transformers=[
            ('num', StandardScaler(), numerical_features),
            ('cat', OneHotEncoder(handle_unknown='ignore'), categorical_features)
        ])
    
    # Train Random Forest model
    rf_model = Pipeline([
        ('preprocessor', preprocessor),
        ('model', RandomForestRegressor(n_estimators=100, random_state=42))
    ])
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Train model
    rf_model.fit(X_train, y_train)
    
    # Evaluate model
    y_pred = rf_model.predict(X_test)
    r2 = r2_score(y_test, y_pred)
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    
    print(f"Random Forest Model - R² Score: {r2:.4f}, RMSE: {rmse:.4f}")
    
    # Extract feature importances
    feature_names = []
    for name, transformer, features in preprocessor.transformers_:
        if name == 'cat':
            # Get all the encoded feature names for categorical variables
            feature_names.extend(transformer.get_feature_names_out(features))
        else:
            # Add numerical feature names directly
            feature_names.extend(features)
    
    # Get feature importances from the Random Forest model
    importances = rf_model.named_steps['model'].feature_importances_
    
    # Map importances back to original features
    original_feature_importances = {}
    for feature in categorical_features + numerical_features:
        # Sum importances for all one-hot encoded features derived from this original feature
        if feature in categorical_features:
            feature_mask = [feature in f for f in feature_names]
            importance = sum(imp for imp, mask in zip(importances, feature_mask) if mask)
        else:
            feature_idx = feature_names.index(feature)
            importance = importances[feature_idx]
        
        original_feature_importances[feature] = importance
    
    # Sort features by importance
    sorted_features = sorted(original_feature_importances.items(), key=lambda x: x[1], reverse=True)
    
    # Visualize feature importances
    plt.figure(figsize=(12, 8))
    features = [x[0] for x in sorted_features]
    importances = [x[1] for x in sorted_features]
    
    plt.barh(features, importances)
    plt.xlabel('Feature Importance')
    plt.title('Feature Importance for DTRK Parameter')
    plt.tight_layout()
    
    # Save feature importance plot
    output_path = os.path.join(ml_dir, 'feature_importance.png')
    plt.savefig(output_path, dpi=300)
    plt.close()
    
    # Analyze shift period specifically
    shift_X = shift_data[features].copy()
    shift_y = shift_data['p285pre2_dtrk'].copy()
    
    # Predict on shift data
    shift_pred = rf_model.predict(shift_X)
    shift_r2 = r2_score(shift_y, shift_pred)
    shift_rmse = np.sqrt(mean_squared_error(shift_y, shift_pred))
    
    print(f"Model performance on 2026-02 shift data - R² Score: {shift_r2:.4f}, RMSE: {shift_rmse:.4f}")
    
    # Feature correlation analysis
    correlation_data = shift_data.copy()
    
    # Convert categorical variables to numeric for correlation analysis
    for col in categorical_features:
        if col in correlation_data.columns:
            correlation_data[col] = correlation_data[col].astype('category').cat.codes
    
    # Calculate correlation with DTRK parameter
    correlations = {}
    for feature in features:
        if feature in correlation_data.columns:
            corr = correlation_data[feature].corr(correlation_data['p285pre2_dtrk'])
            correlations[feature] = corr
    
    # Sort correlations
    sorted_correlations = sorted(correlations.items(), key=lambda x: abs(x[1]), reverse=True)
    
    # Visualize correlations
    plt.figure(figsize=(12, 8))
    features = [x[0] for x in sorted_correlations]
    corr_values = [x[1] for x in sorted_correlations]
    
    plt.barh(features, corr_values)
    plt.xlabel('Correlation with DTRK Parameter')
    plt.title('Feature Correlation with DTRK Parameter during 2026-02')
    plt.axvline(x=0, color='gray', linestyle='--')
    plt.tight_layout()
    
    # Save correlation plot
    output_path = os.path.join(ml_dir, 'feature_correlation.png')
    plt.savefig(output_path, dpi=300)
    plt.close()
    
    return sorted_features, sorted_correlations