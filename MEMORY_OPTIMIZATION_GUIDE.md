# T285 Pipeline Memory Optimization Guide

## 概述

原始的 `pipeline.py` 在处理3G大文件时可能遇到内存不足的问题。优化后的版本包含了多项内存管理改进，可以在有限内存环境下处理大型CSV文件。

## 主要优化内容

### 1. 分块加载 (Chunked Loading)
- **问题**: 原版本一次性加载整个3G文件到内存
- **解决方案**: 
  - 文件 > 2GB 时自动启用分块处理
  - 使用 Polars lazy loading 减少内存占用
  - 每次处理 50,000 行数据块

### 2. 内存监控
- 添加了实时内存使用监控
- 在关键步骤后显示内存使用情况
- 自动垃圾回收释放内存

### 3. 采样策略
- **EDA阶段**: 超过10万行时对目标变量采样
- **交叉验证**: 超过20万行时使用10万样本进行CV
- **相关性分析**: 限制最多50个数值特征

### 4. 优化的数据处理
- 及时删除不需要的中间变量
- 使用 `gc.collect()` 强制垃圾回收
- 限制相关性热图的特征数量

## 系统要求评估

### 最低配置 (3G CSV文件)
- **内存**: 8GB RAM (推荐12GB+)
- **存储**: 至少10GB可用空间
- **CPU**: 4核心以上

### 内存使用估算
```
CSV文件大小 × 3-5 = 峰值内存使用
3GB × 4 = 约12GB峰值内存需求
```

## 使用方法

### 1. 检查系统资源
```python
from memory_config import print_memory_recommendations, get_system_info

# 检查系统信息
system_info = get_system_info()
print(f"可用内存: {system_info['available_memory_gb']:.1f} GB")

# 获取针对3GB文件的建议
print_memory_recommendations(3.0)
```

### 2. 运行优化后的pipeline
```bash
python pipeline.py
```

### 3. 监控内存使用
程序会自动显示各阶段的内存使用情况：
```
Initial memory usage: 1.2 GB
Memory after loading: 8.5 GB
Memory after EDA: 9.1 GB
Memory after CV: 10.2 GB
Final memory usage: 8.8 GB
```

## 配置参数

在 `pipeline.py` 顶部可以调整以下参数：

```python
CHUNK_SIZE = 50000      # 分块大小
MAX_MEMORY_GB = 4       # 最大内存限制
```

## 性能优化建议

### 1. 硬件优化
- **增加内存**: 16GB+ RAM 可显著提升性能
- **SSD存储**: 使用SSD可加快文件读取速度
- **多核CPU**: 利用并行处理能力

### 2. 软件优化
- 关闭其他占用内存的应用程序
- 使用64位Python环境
- 考虑使用虚拟环境隔离依赖

### 3. 数据预处理
- 预先清理不需要的列
- 转换数据类型以减少内存占用
- 考虑使用Parquet格式存储中间结果

## 故障排除

### 内存不足错误
```
MemoryError: Unable to allocate array
```
**解决方案**:
1. 减小 `CHUNK_SIZE` 参数
2. 增加系统虚拟内存
3. 使用更小的采样大小

### 处理速度慢
**优化方案**:
1. 减少交叉验证折数
2. 限制模型复杂度
3. 使用更少的特征

### 磁盘空间不足
**解决方案**:
1. 清理临时文件
2. 压缩输出文件
3. 使用外部存储

## 监控和调试

### 内存使用监控
```python
from memory_config import monitor_memory_usage

@monitor_memory_usage
def your_function():
    # 你的代码
    pass
```

### 系统资源检查
```python
import psutil

# 检查内存使用
memory = psutil.virtual_memory()
print(f"内存使用率: {memory.percent}%")

# 检查磁盘空间
disk = psutil.disk_usage('.')
print(f"磁盘使用率: {disk.percent}%")
```

## 预期性能

### 处理时间估算 (3GB文件)
- **数据加载**: 2-5分钟
- **EDA分析**: 3-8分钟  
- **模型训练**: 10-30分钟
- **特征重要性**: 5-15分钟
- **总计**: 20-60分钟

### 内存使用模式
```
阶段          内存使用    说明
加载          8-12GB     峰值内存使用
EDA           6-10GB     采样后减少
训练          8-15GB     交叉验证期间
完成          4-8GB      清理后
```

## 进一步优化建议

1. **使用Dask**: 对于超大数据集，考虑使用Dask进行分布式处理
2. **特征选择**: 预先进行特征选择减少维度
3. **增量学习**: 使用支持增量学习的算法
4. **云计算**: 考虑使用云平台的高内存实例

## 联系支持

如果遇到内存相关问题，请提供：
1. 系统配置信息
2. 错误日志
3. 内存使用监控输出
4. 数据集大小和特征数量
