<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>T285 DTRK Analysis Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
        }
        .card-header {
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
            border-radius: 15px 15px 0 0 !important;
            font-weight: 600;
        }
        .metric-card {
            text-align: center;
            padding: 1.5rem;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 1rem 0;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: #6c757d;
        }
        .nav-tabs .nav-link {
            border: none;
            border-radius: 10px 10px 0 0;
            margin-right: 2px;
            color: #6c757d;
        }
        .nav-tabs .nav-link.active {
            background: #667eea;
            color: white;
        }
        .feature-importance-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        .importance-bar {
            height: 20px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 10px;
            margin-left: 1rem;
        }
    </style>
</head>
<body>
    <div class="dashboard-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0"><i class="fas fa-chart-line me-3"></i>T285 DTRK Analysis Dashboard</h1>
                    <p class="mb-0 mt-2 opacity-75">Advanced Analytics for DTRK Parameter Shift Detection</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-inline-block">
                        <small class="d-block">Last Updated</small>
                        <span id="lastUpdated" class="fw-bold"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Overview Cards -->
        <div class="row">
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="metric-value" id="totalRows">-</div>
                    <div class="metric-label">Total Records</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="metric-value" id="timeRange">-</div>
                    <div class="metric-label">Time Range</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="metric-value" id="cs1Groups">-</div>
                    <div class="metric-label">CS1 Groups</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="metric-value" id="dtrkMean">-</div>
                    <div class="metric-label">DTRK Mean</div>
                </div>
            </div>
        </div>

        <!-- Main Analysis Tabs -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="analysisTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="time-series-tab" data-bs-toggle="tab" data-bs-target="#time-series" type="button" role="tab">
                                    <i class="fas fa-chart-line me-2"></i>Time Series Analysis
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="shift-analysis-tab" data-bs-toggle="tab" data-bs-target="#shift-analysis" type="button" role="tab">
                                    <i class="fas fa-search me-2"></i>Shift Analysis (2026-02)
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="ml-analysis-tab" data-bs-toggle="tab" data-bs-target="#ml-analysis" type="button" role="tab">
                                    <i class="fas fa-brain me-2"></i>Machine Learning Analysis
                                </button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="analysisTabContent">
                            <!-- Time Series Tab -->
                            <div class="tab-pane fade show active" id="time-series" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-12">
                                        <h5 class="mb-3">DTRK Parameter Time Series</h5>
                                        <div class="chart-container">
                                            <canvas id="timeSeriesChart"></canvas>
                                        </div>
                                        <div class="mt-3">
                                            <button class="btn btn-outline-primary" onclick="refreshTimeSeries()">
                                                <i class="fas fa-sync-alt me-2"></i>Refresh Data
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Shift Analysis Tab -->
                            <div class="tab-pane fade" id="shift-analysis" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h5 class="mb-3">2026-02 Shift Analysis</h5>
                                        <div class="chart-container">
                                            <canvas id="shiftChart"></canvas>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <h5 class="mb-3">Key Insights</h5>
                                        <div id="shiftInsights" class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>
                                            Loading shift analysis data...
                                        </div>
                                        <div class="mt-3">
                                            <button class="btn btn-outline-primary" onclick="refreshShiftAnalysis()">
                                                <i class="fas fa-sync-alt me-2"></i>Refresh Analysis
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- ML Analysis Tab -->
                            <div class="tab-pane fade" id="ml-analysis" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h5 class="mb-3">Feature Importance Analysis</h5>
                                        <div id="featureImportance" class="mb-3">
                                            <div class="loading">
                                                <i class="fas fa-spinner fa-spin me-2"></i>
                                                Loading ML analysis...
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <h5 class="mb-3">Model Performance</h5>
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <div id="modelPerformance">
                                                    <div class="loading">
                                                        <i class="fas fa-spinner fa-spin me-2"></i>
                                                        Loading performance metrics...
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <button class="btn btn-outline-primary" onclick="refreshMLAnalysis()">
                                                <i class="fas fa-sync-alt me-2"></i>Retrain Model
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let timeSeriesChart = null;
        let shiftChart = null;
        let overviewData = {};

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            updateLastUpdated();
            loadOverviewData();
            
            // Set up tab change listeners
            document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
                tab.addEventListener('shown.bs.tab', function(e) {
                    const target = e.target.getAttribute('data-bs-target');
                    if (target === '#time-series') {
                        loadTimeSeriesData();
                    } else if (target === '#shift-analysis') {
                        loadShiftAnalysisData();
                    } else if (target === '#ml-analysis') {
                        loadMLAnalysisData();
                    }
                });
            });

            // Load initial data
            loadTimeSeriesData();
        });

        function updateLastUpdated() {
            const now = new Date();
            document.getElementById('lastUpdated').textContent = now.toLocaleString();
        }

        function loadOverviewData() {
            fetch('/api/overview')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error loading overview:', data.error);
                        return;
                    }
                    
                    overviewData = data;
                    document.getElementById('totalRows').textContent = data.total_rows.toLocaleString();
                    document.getElementById('timeRange').textContent = data.time_range;
                    document.getElementById('cs1Groups').textContent = data.cs1_groups;
                    document.getElementById('dtrkMean').textContent = data.dtrk_mean.toFixed(3);
                })
                .catch(error => console.error('Error:', error));
        }

        function loadTimeSeriesData() {
            fetch('/api/time_series')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error loading time series:', data.error);
                        return;
                    }
                    
                    renderTimeSeriesChart(data);
                })
                .catch(error => console.error('Error:', error));
        }

        function renderTimeSeriesChart(data) {
            const ctx = document.getElementById('timeSeriesChart').getContext('2d');
            
            if (timeSeriesChart) {
                timeSeriesChart.destroy();
            }

            const datasets = [];
            
            // Add CS1 group data
            data.cs1_groups.forEach((group, index) => {
                if (data.pivot_table[group]) {
                    datasets.push({
                        label: `CS1 Group: ${group}`,
                        data: Object.values(data.pivot_table[group]),
                        borderColor: `hsl(${index * 360 / data.cs1_groups.length}, 70%, 50%)`,
                        backgroundColor: `hsla(${index * 360 / data.cs1_groups.length}, 70%, 50%, 0.1)`,
                        tension: 0.4,
                        fill: false
                    });
                }
            });

            // Add overall mean
            if (data.overall_mean) {
                const overallData = data.overall_mean.map(item => item.p285pre2_dtrk);
                datasets.push({
                    label: 'Overall Mean',
                    data: overallData,
                    borderColor: '#000000',
                    backgroundColor: 'rgba(0, 0, 0, 0.1)',
                    borderWidth: 2,
                    borderDash: [5, 5],
                    tension: 0.4,
                    fill: false
                });
            }

            timeSeriesChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.weeks,
                    datasets: datasets
                },
                options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'DTRK Parameter Time Series by CS1 Group'
                            },
                            legend: {
                                position: 'top'
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false,
                                callbacks: {
                                    title: function(context) {
                                        return 'Week: ' + context[0].label;
                                    },
                                    label: function(context) {
                                        return context.dataset.label + ': ' + context.parsed.y.toFixed(3);
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: 'Time (Week)'
                                }
                            },
                            y: {
                                title: {
                                    display: true,
                                    text: 'DTRK Parameter Mean'
                                }
                            }
                        },
                        elements: {
                            point: {
                                radius: 3,
                                hoverRadius: 6
                            }
                        },
                        interaction: {
                            mode: 'nearest',
                            axis: 'x',
                            intersect: false
                        }
                    }
            });
        }

        function loadShiftAnalysisData() {
            fetch('/api/shift_analysis')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error loading shift analysis:', data.error);
                        return;
                    }
                    
                    renderShiftChart(data);
                    updateShiftInsights(data);
                })
                .catch(error => console.error('Error:', error));
        }

        function renderShiftChart(data) {
            const ctx = document.getElementById('shiftChart').getContext('2d');
            
            if (shiftChart) {
                shiftChart.destroy();
            }

            const labels = data.map(item => item.cs1_group);
            const means = data.map(item => item.mean);
            const stds = data.map(item => item.std);

            shiftChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Mean DTRK',
                        data: means,
                        backgroundColor: 'rgba(102, 126, 234, 0.8)',
                        borderColor: 'rgba(102, 126, 234, 1)',
                        borderWidth: 1,
                        std_dev: stds  // Custom property to store standard deviations
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'DTRK Parameter Means by CS1 Group (2026-02)'
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    return 'CS1 Group: ' + context[0].label;
                                },
                                label: function(context) {
                                    const data = context.raw;
                                    const std = context.dataset.std_dev[context.dataIndex];
                                    return [
                                        'Mean: ' + data.toFixed(3),
                                        'Std Dev: ' + std.toFixed(3)
                                    ];
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'CS1 Group'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'DTRK Parameter Mean'
                            }
                        }
                    },
                    elements: {
                        bar: {
                            borderWidth: 1
                        }
                    }
                }
            });
        }

        function updateShiftInsights(data) {
            const insights = document.getElementById('shiftInsights');
            
            if (data.length === 0) {
                insights.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>No data available for 2026-02 period.';
                insights.className = 'alert alert-warning';
                return;
            }

            const means = data.map(item => item.mean);
            const stds = data.map(item => item.std);
            const counts = data.map(item => item.count);
            
            const maxMean = Math.max(...means);
            const minMean = Math.min(...means);
            const maxGroup = data.find(item => item.mean === maxMean).cs1_group;
            const minGroup = data.find(item => item.mean === minMean).cs1_group;
            
            const avgMean = means.reduce((a, b) => a + b, 0) / means.length;
            const avgStd = stds.reduce((a, b) => a + b, 0) / stds.length;
            
            let html = `
                <i class="fas fa-lightbulb me-2"></i>
                <strong>Key Findings for 2026-02:</strong><br>
                • Highest DTRK mean: <strong>${maxGroup}</strong> (${maxMean.toFixed(3)})<br>
                • Lowest DTRK mean: <strong>${minGroup}</strong> (${minMean.toFixed(3)})<br>
                • Average DTRK mean: <strong>${avgMean.toFixed(3)}</strong><br>
                • Average standard deviation: <strong>${avgStd.toFixed(3)}</strong><br>
                • Range: <strong>${(maxMean - minMean).toFixed(3)}</strong>
            `;
            
            insights.innerHTML = html;
            insights.className = 'alert alert-info';
        }

        function loadMLAnalysisData() {
            fetch('/api/ml_analysis')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error loading ML analysis:', data.error);
                        return;
                    }
                    
                    renderFeatureImportance(data);
                    updateModelPerformance(data);
                })
                .catch(error => console.error('Error:', error));
        }

        function renderFeatureImportance(data) {
            const container = document.getElementById('featureImportance');
            
            if (!data.feature_importances || data.feature_importances.length === 0) {
                container.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>No feature importance data available.</div>';
                return;
            }

            let html = '<div class="feature-importance-list">';
            
            data.feature_importances.forEach(([feature, importance]) => {
                const percentage = (importance * 100).toFixed(1);
                html += `
                    <div class="feature-importance-item">
                        <span>${feature}</span>
                        <div style="display: flex; align-items: center;">
                            <span class="text-muted me-2">${percentage}%</span>
                            <div class="importance-bar" style="width: ${percentage * 2}px;"></div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }

        function updateModelPerformance(data) {
            const container = document.getElementById('modelPerformance');
            
            if (!data.model_performance) {
                container.innerHTML = '<div class="text-muted">No performance data available.</div>';
                return;
            }

            const perf = data.model_performance;
            container.innerHTML = `
                <div class="mb-2">
                    <strong>R² Score:</strong> ${perf.r2_score.toFixed(3)}
                </div>
                <div class="mb-2">
                    <strong>RMSE:</strong> ${perf.rmse.toFixed(3)}
                </div>
                <div class="progress mb-2" style="height: 20px;">
                    <div class="progress-bar bg-success" role="progressbar" 
                         style="width: ${Math.max(0, Math.min(100, perf.r2_score * 100))}%"
                         aria-valuenow="${perf.r2_score}" aria-valuemin="0" aria-valuemax="1">
                        ${(perf.r2_score * 100).toFixed(1)}%
                    </div>
                </div>
                <small class="text-muted">Model accuracy based on R² score</small>
            `;
        }

        // Refresh functions
        function refreshTimeSeries() {
            loadTimeSeriesData();
            updateLastUpdated();
        }

        function refreshShiftAnalysis() {
            loadShiftAnalysisData();
            updateLastUpdated();
        }

        function refreshMLAnalysis() {
            loadMLAnalysisData();
            updateLastUpdated();
        }
    </script>
</body>
</html>