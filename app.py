#!/usr/bin/env python
# -*- coding: utf-8 -*-

from flask import Flask, render_template, jsonify, request, send_file
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os
import io
import base64
from matplotlib.backends.backend_agg import FigureCanvasAgg as FigureCanvas
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

app = Flask(__name__)

# Global variables to store data
df = None
shift_data = None
analysis_results = {}

# Set up matplotlib for web use
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_preprocess_data():
    """Load and preprocess data"""
    global df
    try:
        # Load data
        data_file = os.path.join(os.path.dirname(__file__), 'T285.csv')
        df = pd.read_csv(data_file)
        
        # Data preprocessing
        df['p285pre2_dtrk'] = pd.to_numeric(df['p285pre2_dtrk'], errors='coerce')
        df = df.dropna(subset=['p285pre2_dtrk'])
        
        if 'cs1_id' in df.columns:
            df['cs1_group'] = df['cs1_id'].str[:4]
        
        return True
    except Exception as e:
        print(f"Error loading data: {e}")
        return False

def get_time_series_data():
    """Get time series analysis data"""
    if df is None:
        return None
    
    # Group by week and CS1 group
    time_analysis = df.groupby(['scopy_st_week', 'cs1_group'])['p285pre2_dtrk'].agg(['mean', 'std', 'count']).reset_index()
    pivot_table = time_analysis.pivot(index='scopy_st_week', columns='cs1_group', values='mean')
    overall_mean = df.groupby('scopy_st_week')['p285pre2_dtrk'].mean().reset_index()
    
    return {
        'pivot_table': pivot_table.to_dict(),
        'overall_mean': overall_mean.to_dict('records'),
        'weeks': pivot_table.index.tolist(),
        'cs1_groups': pivot_table.columns.tolist()
    }

def get_shift_analysis_data(target_period='2026-02'):
    """Get shift analysis data for specific period"""
    global shift_data
    
    if df is None:
        return None
    
    # Filter target period data
    shift_data = df[df['scopy_st_week'] == target_period].copy()
    
    # Calculate statistics by CS1 group
    shift_stats = shift_data.groupby('cs1_group')['p285pre2_dtrk'].agg(['mean', 'std', 'count', 'min', 'max']).reset_index()
    
    # Calculate comparison with previous period
    all_periods = sorted(df['scopy_st_week'].unique())
    if target_period in all_periods:
        target_idx = all_periods.index(target_period)
        before_period = all_periods[target_idx-1] if target_idx > 0 else None
        
        if before_period:
            before_data = df[df['scopy_st_week'] == before_period]
            before_stats = before_data.groupby('cs1_group')['p285pre2_dtrk'].mean().reset_index()
            before_stats.columns = ['cs1_group', 'before_mean']
            
            shift_stats = pd.merge(shift_stats, before_stats, on='cs1_group', how='left')
            shift_stats['change_from_before'] = shift_stats['mean'] - shift_stats['before_mean']
    
    return shift_stats.to_dict('records')

def get_ml_analysis_data():
    """Get machine learning analysis data"""
    if df is None or shift_data is None:
        return None
    
    # Select features
    potential_features = [
        'drive_part_num', 'drive_sbr_num', 'cms_config', 'stack_part_num',
        'hga_supplier', 'motor_vend_id', 'disc_install_id', 'tester_id',
        'interface', 'product', 'capacity', 'cs1_group'
    ]
    
    features = [col for col in potential_features if col in df.columns]
    
    if not features:
        return None
    
    # Prepare data
    X = df[features].copy()
    y = df['p285pre2_dtrk'].copy()
    
    # Handle categorical features
    categorical_features = X.select_dtypes(include=['object']).columns.tolist()
    numerical_features = X.select_dtypes(exclude=['object']).columns.tolist()
    
    # Create preprocessor
    preprocessor = ColumnTransformer(
        transformers=[
            ('num', StandardScaler(), numerical_features),
            ('cat', OneHotEncoder(handle_unknown='ignore'), categorical_features)
        ])
    
    # Train model
    rf_model = Pipeline([
        ('preprocessor', preprocessor),
        ('model', RandomForestRegressor(n_estimators=100, random_state=42))
    ])
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    rf_model.fit(X_train, y_train)
    
    # Evaluate model
    y_pred = rf_model.predict(X_test)
    r2 = r2_score(y_test, y_pred)
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    
    # Extract feature importances
    feature_names = []
    for name, transformer, features in preprocessor.transformers_:
        if name == 'cat':
            feature_names.extend(transformer.get_feature_names_out(features))
        else:
            feature_names.extend(features)
    
    importances = rf_model.named_steps['model'].feature_importances_
    
    # Map back to original features
    original_feature_importances = {}
    for feature in categorical_features + numerical_features:
        if feature in categorical_features:
            feature_mask = [feature in f for f in feature_names]
            importance = sum(imp for imp, mask in zip(importances, feature_mask) if mask)
        else:
            feature_idx = feature_names.index(feature)
            importance = importances[feature_idx]
        
        original_feature_importances[feature] = importance
    
    # Sort by importance
    sorted_features = sorted(original_feature_importances.items(), key=lambda x: x[1], reverse=True)
    
    return {
        'feature_importances': sorted_features,
        'model_performance': {
            'r2_score': r2,
            'rmse': rmse
        }
    }

@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('dashboard.html')

@app.route('/api/overview')
def api_overview():
    """Get data overview"""
    if df is None:
        if not load_and_preprocess_data():
            return jsonify({'error': 'Failed to load data'})
    
    return jsonify({
        'total_rows': len(df),
        'time_range': f"{df['scopy_st_week'].min()} to {df['scopy_st_week'].max()}",
        'cs1_groups': df['cs1_group'].nunique() if 'cs1_group' in df.columns else 0,
        'dtrk_mean': df['p285pre2_dtrk'].mean(),
        'dtrk_std': df['p285pre2_dtrk'].std()
    })

@app.route('/api/time_series')
def api_time_series():
    """Get time series data"""
    if df is None:
        if not load_and_preprocess_data():
            return jsonify({'error': 'Failed to load data'})
    
    data = get_time_series_data()
    return jsonify(data)

@app.route('/api/shift_analysis')
def api_shift_analysis():
    """Get shift analysis data"""
    if df is None:
        if not load_and_preprocess_data():
            return jsonify({'error': 'Failed to load data'})
    
    target_period = request.args.get('period', '2026-02')
    data = get_shift_analysis_data(target_period)
    return jsonify(data)

@app.route('/api/ml_analysis')
def api_ml_analysis():
    """Get machine learning analysis data"""
    if df is None:
        if not load_and_preprocess_data():
            return jsonify({'error': 'Failed to load data'})
    
    data = get_ml_analysis_data()
    return jsonify(data)

@app.route('/api/chart/<chart_type>')
def api_chart(chart_type):
    """Generate charts"""
    if df is None:
        if not load_and_preprocess_data():
            return jsonify({'error': 'Failed to load data'})
    
    try:
        fig, ax = plt.subplots(figsize=(12, 6))
        
        if chart_type == 'time_series':
            # Time series chart
            time_data = get_time_series_data()
            if time_data:
                for cs1_group in time_data['cs1_groups']:
                    if cs1_group in time_data['pivot_table']:
                        weeks = time_data['weeks']
                        values = time_data['pivot_table'][cs1_group]
                        ax.plot(weeks, values, label=f'CS1 Group: {cs1_group}', alpha=0.7)
                
                # Add overall mean
                overall_data = pd.DataFrame(time_data['overall_mean'])
                ax.plot(overall_data['scopy_st_week'], overall_data['p285pre2_dtrk'], 
                       'k--', linewidth=2, label='Overall Mean')
                
                # Mark 2026-02
                if '2026-02' in time_data['weeks']:
                    ax.axvline(x='2026-02', color='red', linestyle='--', alpha=0.5, 
                              label='2026-02 Shift Point')
                
                ax.set_title('DTRK Parameter Time Series by CS1_ID Group')
                ax.set_xlabel('Time (Week)')
                ax.set_ylabel('DTRK Parameter Mean')
                ax.legend()
                ax.grid(True, alpha=0.3)
        
        elif chart_type == 'shift_means':
            # Shift analysis chart
            shift_data = get_shift_analysis_data()
            if shift_data:
                shift_df = pd.DataFrame(shift_data)
                shift_df = shift_df.sort_values('mean')
                
                bars = ax.bar(shift_df['cs1_group'], shift_df['mean'], 
                             yerr=shift_df['std'], alpha=0.7, capsize=5)
                
                ax.set_title('DTRK Parameter Means by CS1 Group during 2026-02')
                ax.set_xlabel('CS1 Group')
                ax.set_ylabel('DTRK Parameter Mean')
                ax.grid(True, alpha=0.3, axis='y')
        
        elif chart_type == 'feature_importance':
            # Feature importance chart
            ml_data = get_ml_analysis_data()
            if ml_data and ml_data['feature_importances']:
                features = [x[0] for x in ml_data['feature_importances']]
                importances = [x[1] for x in ml_data['feature_importances']]
                
                ax.barh(features, importances)
                ax.set_xlabel('Feature Importance')
                ax.set_title('Feature Importance for DTRK Parameter')
        
        plt.tight_layout()
        
        # Convert to base64 image
        img = io.BytesIO()
        FigureCanvas(fig).print_png(img)
        img.seek(0)
        plt.close(fig)
        
        return send_file(img, mimetype='image/png')
    
    except Exception as e:
        return jsonify({'error': str(e)})

if __name__ == '__main__':
    # Load data on startup
    load_and_preprocess_data()
    
    # Run the Flask app
    app.run(debug=True, host='0.0.0.0', port=5000)