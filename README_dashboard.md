# T285 DTRK Analysis Flask Dashboard

## Overview
This Flask web dashboard provides an interactive interface for analyzing T285 DTRK parameter shift phenomena, with a focus on the 2026-02 shift period.

## Features

### 1. Overview Dashboard
- **Total Records**: Shows the total number of data records
- **Time Range**: Displays the analysis time period
- **CS1 Groups**: Number of unique CS1 groups in the data
- **DTRK Mean**: Average DTRK parameter value

### 2. Time Series Analysis
- Interactive line chart showing DTRK parameter trends over time
- Grouped by CS1_ID groups for detailed analysis
- Overall mean trend line
- Highlighted 2026-02 shift point
- Real-time data refresh capability

### 3. Shift Analysis (2026-02)
- Detailed analysis of the 2026-02 shift period
- Bar chart showing DTRK means by CS1 group
- Statistical insights and key findings
- Standard deviation visualization

### 4. Machine Learning Analysis
- Feature importance analysis using Random Forest
- Model performance metrics (R² score, RMSE)
- Interactive feature importance ranking
- Model accuracy visualization

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. Ensure the T285.csv data file is in the project root directory

3. Run the Flask application:
```bash
python app.py
```

4. Open your web browser and navigate to:
```
http://localhost:5000
```

## Usage

### Navigation
- Use the tab navigation to switch between different analysis views
- Each tab provides specific insights into the DTRK parameter data

### Interactive Features
- **Refresh Buttons**: Update data and charts in real-time
- **Hover Effects**: Get detailed information on chart elements
- **Responsive Design**: Optimized for desktop and mobile devices

### Data Analysis
1. **Time Series Tab**: Monitor DTRK parameter trends over time
2. **Shift Analysis Tab**: Investigate the 2026-02 shift phenomenon
3. **ML Analysis Tab**: Understand which factors influence DTRK parameters

## Technical Details

### Backend (Flask)
- RESTful API endpoints for data access
- Machine learning integration with scikit-learn
- Real-time data processing and visualization
- Error handling and data validation

### Frontend (Bootstrap + Chart.js)
- Modern, responsive UI design
- Interactive charts and visualizations
- Real-time data updates
- Mobile-friendly interface

### Machine Learning
- Random Forest Regression for feature importance
- Cross-validation and model evaluation
- Feature preprocessing and encoding
- Performance metrics tracking

## API Endpoints

- `GET /api/overview`: Get data overview statistics
- `GET /api/time_series`: Get time series analysis data
- `GET /api/shift_analysis`: Get shift analysis data
- `GET /api/ml_analysis`: Get machine learning analysis results
- `GET /api/chart/<chart_type>`: Generate specific chart images

## File Structure
```
├── app.py                 # Main Flask application
├── templates/
│   └── dashboard.html     # Dashboard HTML template
├── requirements.txt       # Python dependencies
└── README_dashboard.md   # This file
```

## Notes
- Ensure T285.csv file is available in the project directory
- The dashboard automatically loads data on startup
- Charts and analysis are generated in real-time
- All visualizations use English labels to avoid encoding issues

## Troubleshooting

### Common Issues
1. **Data Loading Error**: Check if T285.csv exists and is properly formatted
2. **Port Already in Use**: Change port in app.py or stop other Flask instances
3. **Chart Not Loading**: Refresh the page or check browser console for errors

### Performance Tips
- The dashboard loads data on startup for better performance
- Large datasets may take time to process initially
- Use refresh buttons to update specific analysis sections

## Future Enhancements
- Additional machine learning algorithms
- Advanced filtering and search capabilities
- Export functionality for charts and reports
- Real-time data streaming integration