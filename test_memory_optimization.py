"""
Test script to validate memory optimization improvements
"""

import time
import pandas as pd
import numpy as np
from pathlib import Path
from memory_config import get_system_info, print_memory_recommendations, get_optimal_settings

def create_test_data(size_gb: float = 0.1, filename: str = "test_small.csv"):
    """Create a test CSV file of specified size"""
    print(f"Creating test file: {filename} (~{size_gb} GB)")
    
    # Estimate rows needed (rough approximation)
    # Assuming ~100 bytes per row on average
    rows_needed = int(size_gb * 1024**3 / 100)
    
    # Create synthetic data similar to the real dataset
    np.random.seed(42)
    
    data = {
        'p285pre2_dtrk': np.random.normal(0, 1, rows_needed),  # Target variable
        'numeric_1': np.random.normal(100, 20, rows_needed),
        'numeric_2': np.random.exponential(2, rows_needed),
        'numeric_3': np.random.uniform(0, 1000, rows_needed),
        'numeric_4': np.random.normal(50, 10, rows_needed),
        'numeric_5': np.random.gamma(2, 2, rows_needed),
        'categorical_1': np.random.choice(['A', 'B', 'C', 'D'], rows_needed),
        'categorical_2': np.random.choice(['Type1', 'Type2', 'Type3'], rows_needed),
        'categorical_3': np.random.choice(['X', 'Y', 'Z'], rows_needed),
        'stack_part_num': np.random.randint(1000, 9999, rows_needed),
        'capacity': np.random.choice([500, 1000, 2000], rows_needed),
    }
    
    # Add more numeric columns to reach target size
    for i in range(6, 20):
        data[f'numeric_{i}'] = np.random.normal(0, 1, rows_needed)
    
    df = pd.DataFrame(data)
    df.to_csv(filename, index=False)
    
    actual_size = Path(filename).stat().st_size / (1024**3)
    print(f"Created {filename}: {actual_size:.3f} GB, {len(df):,} rows, {len(df.columns)} columns")
    return filename

def test_original_vs_optimized():
    """Compare original and optimized pipeline performance"""
    print("=== Memory Optimization Test ===\n")
    
    # System info
    system_info = get_system_info()
    print(f"System: {system_info['total_memory_gb']:.1f} GB RAM, {system_info['cpu_count']} cores")
    print(f"Available: {system_info['available_memory_gb']:.1f} GB\n")
    
    # Test with different file sizes
    test_sizes = [0.1, 0.5, 1.0]  # GB
    
    for size in test_sizes:
        print(f"\n--- Testing with {size} GB file ---")
        
        # Create test file
        test_file = f"test_{size}gb.csv"
        create_test_data(size, test_file)
        
        # Get optimization settings
        settings = get_optimal_settings(size)
        print(f"Chunked processing: {settings['use_chunked_processing']}")
        print(f"Sampling: {settings['use_sampling']}")
        print(f"Chunk size: {settings['chunk_size']:,}")
        
        # Test loading performance
        print("\nTesting data loading...")
        start_time = time.time()
        
        try:
            if settings['use_chunked_processing']:
                from pipeline import load_data_chunked
                df = load_data_chunked(test_file)
            else:
                import polars as pl
                df = pl.read_csv(test_file).to_pandas()
            
            load_time = time.time() - start_time
            print(f"✓ Loaded successfully in {load_time:.2f}s")
            print(f"  Shape: {df.shape}")
            
            # Memory usage
            memory_usage = df.memory_usage(deep=True).sum() / (1024**3)
            print(f"  Memory usage: {memory_usage:.3f} GB")
            
            # Clean up
            del df
            
        except Exception as e:
            print(f"✗ Loading failed: {str(e)}")
        
        # Clean up test file
        Path(test_file).unlink(missing_ok=True)

def benchmark_chunked_loading():
    """Benchmark chunked vs normal loading"""
    print("\n=== Chunked Loading Benchmark ===")
    
    # Create a moderately large test file
    test_file = "benchmark_test.csv"
    create_test_data(0.5, test_file)
    
    # Test normal loading
    print("\n1. Normal loading (Polars):")
    start_time = time.time()
    try:
        import polars as pl
        df_normal = pl.read_csv(test_file).to_pandas()
        normal_time = time.time() - start_time
        normal_memory = df_normal.memory_usage(deep=True).sum() / (1024**3)
        print(f"   Time: {normal_time:.2f}s")
        print(f"   Memory: {normal_memory:.3f} GB")
        del df_normal
    except Exception as e:
        print(f"   Failed: {str(e)}")
        normal_time = float('inf')
    
    # Test chunked loading
    print("\n2. Chunked loading:")
    start_time = time.time()
    try:
        from pipeline import load_data_chunked
        df_chunked = load_data_chunked(test_file)
        chunked_time = time.time() - start_time
        chunked_memory = df_chunked.memory_usage(deep=True).sum() / (1024**3)
        print(f"   Time: {chunked_time:.2f}s")
        print(f"   Memory: {chunked_memory:.3f} GB")
        del df_chunked
    except Exception as e:
        print(f"   Failed: {str(e)}")
        chunked_time = float('inf')
    
    # Comparison
    if normal_time != float('inf') and chunked_time != float('inf'):
        speedup = normal_time / chunked_time
        print(f"\nSpeedup: {speedup:.2f}x {'(chunked faster)' if speedup > 1 else '(normal faster)'}")
    
    # Clean up
    Path(test_file).unlink(missing_ok=True)

def test_memory_monitoring():
    """Test memory monitoring functionality"""
    print("\n=== Memory Monitoring Test ===")
    
    from memory_config import monitor_memory_usage
    
    @monitor_memory_usage
    def memory_intensive_task():
        # Create some data to use memory
        data = np.random.random((10000, 100))
        df = pd.DataFrame(data)
        result = df.sum().sum()
        return result
    
    print("Running memory-intensive task...")
    result = memory_intensive_task()
    print(f"Task result: {result:.2f}")

def main():
    """Run all tests"""
    print("T285 Pipeline Memory Optimization Tests")
    print("=" * 50)
    
    try:
        # Check if required packages are available
        import polars as pl
        import psutil
        print("✓ All required packages available")
    except ImportError as e:
        print(f"✗ Missing package: {e}")
        print("Please install: pip install polars psutil")
        return
    
    # Run tests
    test_original_vs_optimized()
    benchmark_chunked_loading()
    test_memory_monitoring()
    
    print("\n" + "=" * 50)
    print("Tests completed!")
    
    # Final recommendations
    print("\nRecommendations for 3GB file:")
    print_memory_recommendations(3.0)

if __name__ == "__main__":
    main()
