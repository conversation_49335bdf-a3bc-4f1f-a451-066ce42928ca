
"""
dtrk end-to-end pipeline
------------------------
Usage:
    1) Place your CSV as "test.csv" in the working directory (must include column 'dtrk').
    2) Run:  python dtrk_pipeline.py
    3) Outputs will be written to ./outputs/

What it does:
    - EDA: missing values summary, distributions, correlation heatmap
    - Modeling: Lasso, Ridge, RandomForest, HistGradientBoosting with KFold CV
    - Feature importance: permutation importance, linear coefficients
    - Control/Optimization: suggest factor values (within learned ranges) to drive predicted dtrk -> 0
"""

import warnings
warnings.filterwarnings("ignore")
from pathlib import Path
import json
import math
import time
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional
import polars as pl
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

from sklearn.model_selection import KFold, cross_validate, train_test_split
from sklearn.pipeline import Pipeline
from sklearn.compose import ColumnTransformer
from sklearn.impute import SimpleImputer
from sklearn.preprocessing import <PERSON><PERSON>ot<PERSON>ncoder, StandardScaler
from sklearn.linear_model import LassoCV, RidgeCV
from sklearn.ensemble import RandomForestRegressor, HistGradientBoostingRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.inspection import permutation_importance

try:
    from scipy.optimize import minimize
    SCIPY_OK = True
except Exception:
    SCIPY_OK = False


TARGET = "p285pre2_dtrk"
CSV_PATH = "test.csv"
OUT_DIR = Path("outputs")
OUT_DIR.mkdir(exist_ok=True)


def load_data(path: str) -> pd.DataFrame:
    if not Path(path).exists():
        raise FileNotFoundError(f"'{path}' not found. Please upload your file as {path}.")
    df = pl.read_csv(path)
    # df = pd.read_csv(path)
    if TARGET not in df.columns:
        raise ValueError(f"Target column '{TARGET}' not found in CSV. Available columns: {list(df.columns)}")
    return df


def split_columns(df: pd.DataFrame) -> Tuple[List[str], List[str]]:
    # Heuristic: numeric dtypes are numeric; others are categorical
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    if TARGET in numeric_cols:
        numeric_cols.remove(TARGET)
    categorical_cols = [c for c in df.columns if c not in numeric_cols + [TARGET]]
    return numeric_cols, categorical_cols


def eda(df: pd.DataFrame, numeric_cols: List[str]) -> None:
    # Missing values summary
    miss = df.isna().sum().sort_values(ascending=False)
    miss.to_csv(OUT_DIR / "missing_values_summary.csv")

    # Target distribution
    plt.figure(figsize=(6,4))
    df[TARGET].dropna().hist(bins=50)
    plt.title("Target distribution: dtrk")
    plt.xlabel("dtrk")
    plt.ylabel("count")
    plt.tight_layout()
    plt.savefig(OUT_DIR / "dtrk_distribution.png", dpi=150)
    plt.close()

    # Correlation (numeric only)
    if len(numeric_cols) > 0:
        corr = df[numeric_cols + [TARGET]].corr(numeric_only=True)
        corr.to_csv(OUT_DIR / "correlation_numeric.csv")

        # Heatmap
        plt.figure(figsize=(max(6, 0.35*len(corr.columns)), max(5, 0.35*len(corr.columns))))
        im = plt.imshow(corr, interpolation="nearest")
        plt.xticks(range(len(corr.columns)), corr.columns, rotation=90)
        plt.yticks(range(len(corr.columns)), corr.columns)
        plt.colorbar(im, fraction=0.046, pad=0.04)
        plt.title("Correlation heatmap (numeric)")
        plt.tight_layout()
        plt.savefig(OUT_DIR / "correlation_heatmap.png", dpi=150)
        plt.close()


def build_preprocessor(numeric_cols: List[str], categorical_cols: List[str]) -> ColumnTransformer:
    numeric_tf = Pipeline(steps=[
        ("imputer", SimpleImputer(strategy="median")),
        ("scaler", StandardScaler()),
    ])
    categorical_tf = Pipeline(steps=[
        ("imputer", SimpleImputer(strategy="most_frequent")),
        ("ohe", OneHotEncoder(handle_unknown="ignore")),
    ])
    pre = ColumnTransformer(
        transformers=[
            ("num", numeric_tf, numeric_cols),
            ("cat", categorical_tf, categorical_cols),
        ]
    )
    return pre


def model_zoo(random_state: int = 42) -> Dict[str, object]:
    models = {
        "LassoCV": LassoCV(alphas=None, cv=5, max_iter=5000, random_state=random_state),
        "RidgeCV": RidgeCV(alphas=np.logspace(-3, 3, 25), cv=None),
        "RandomForest": RandomForestRegressor(
            n_estimators=400, max_depth=None, min_samples_leaf=2, n_jobs=-1, random_state=random_state
        ),
        "HistGB": HistGradientBoostingRegressor(random_state=random_state)
    }
    return models


def evaluate_models(X: pd.DataFrame, y: pd.Series, preprocessor: ColumnTransformer, random_state: int = 42) -> pd.DataFrame:
    cv = KFold(n_splits=5, shuffle=True, random_state=random_state)
    records = []
    for name, model in model_zoo(random_state).items():
        pipe = Pipeline(steps=[("pre", preprocessor), ("model", model)])
        scores = cross_validate(
            pipe, X, y,
            cv=cv,
            scoring=("neg_root_mean_squared_error", "neg_mean_absolute_error", "r2"),
            n_jobs=-1,
            return_train_score=False
        )
        rec = {
            "model": name,
            "rmse": -scores["test_neg_root_mean_squared_error"].mean(),
            "mae": -scores["test_neg_mean_absolute_error"].mean(),
            "r2": scores["test_r2"].mean(),
        }
        records.append(rec)
    res = pd.DataFrame(records).sort_values(["rmse", "mae"])
    res.to_csv(OUT_DIR / "cv_results.csv", index=False)
    return res


def fit_best_model(X: pd.DataFrame, y: pd.Series, preprocessor: ColumnTransformer, results: pd.DataFrame):
    best_name = results.iloc[0]["model"]
    model = model_zoo()[best_name]
    pipe = Pipeline(steps=[("pre", preprocessor), ("model", model)])
    pipe.fit(X, y)
    return best_name, pipe


def get_feature_names(pre: ColumnTransformer, X: pd.DataFrame) -> List[str]:
    # Build feature names after preprocessing
    out = []
    # numeric
    for name, trans, cols in pre.transformers_:
        if name == "num":
            out.extend(cols)
        elif name == "cat":
            # handle OneHotEncoder names
            enc = trans.named_steps.get("ohe")
            if enc is not None and hasattr(enc, "get_feature_names_out"):
                cats = enc.get_feature_names_out(cols)
                out.extend(list(cats))
    return out


def feature_importance(pipe: Pipeline, X: pd.DataFrame, y: pd.Series) -> pd.DataFrame:
    # Permutation importance on a holdout split for stability
    X_tr, X_te, y_tr, y_te = train_test_split(X, y, test_size=0.3, random_state=42)
    pipe.fit(X_tr, y_tr)
    pre = pipe.named_steps["pre"]
    feature_names = get_feature_names(pre, X_tr)

    r = permutation_importance(pipe, X_te, y_te, n_repeats=10, random_state=42, n_jobs=-1)
    imp_df = pd.DataFrame({
        "feature": feature_names,
        "importance_mean": r.importances_mean,
        "importance_std": r.importances_std,
    }).sort_values("importance_mean", ascending=False)
    imp_df.to_csv(OUT_DIR / "feature_importance_permutation.csv", index=False)

    # If linear model, also export coefficients for interpretability
    model = pipe.named_steps["model"]
    if hasattr(model, "coef_"):
        # Refit on all data
        pipe.fit(X, y)
        # Transform X to get the same feature space
        X_tx = pipe.named_steps["pre"].transform(X)
        coefs = getattr(model, "coef_", None)
        if coefs is not None:
            coef_df = pd.DataFrame({
                "feature": feature_names,
                "coef": coefs.ravel() if hasattr(coefs, "ravel") else np.array(coefs)
            }).sort_values("coef", key=lambda s: np.abs(s), ascending=False)
            coef_df.to_csv(OUT_DIR / "linear_coefficients.csv", index=False)

    return imp_df


@dataclass
class Bounds:
    low: float
    high: float


def estimate_bounds(df: pd.DataFrame, numeric_cols: List[str]) -> Dict[str, Bounds]:
    bounds = {}
    for c in numeric_cols:
        s = df[c].dropna()
        if s.empty:
            continue
        low = s.quantile(0.01)
        high = s.quantile(0.99)
        if math.isfinite(low) and math.isfinite(high) and low < high:
            bounds[c] = Bounds(low=float(low), high=float(high))
    return bounds


def suggest_to_zero(pipe: Pipeline,
                    X: pd.DataFrame,
                    numeric_cols: List[str],
                    bounds: Dict[str, Bounds],
                    anchor_x: Optional[pd.Series] = None,
                    n_starts: int = 20,
                    step_penalty: float = 1e-3,
                    random_state: int = 42) -> Tuple[pd.Series, float]:
    """
    Find a numeric factor vector that drives prediction near 0 while staying within bounds.
    Minimizes: |f(x) - 0| + step_penalty * ||x - x0||^2
    x0 = anchor vector (median if None). Only numeric columns are optimized; categoricals (if any) are held at mode.
    """
    rng = np.random.RandomState(random_state)

    # Build a baseline row: median for numeric, mode for categorical
    med = X[numeric_cols].median(numeric_only=True)
    mode_vals = X.drop(columns=numeric_cols, errors="ignore").mode(dropna=True)
    baseline = pd.Series(index=X.columns, dtype=object)
    baseline[numeric_cols] = med
    if not mode_vals.empty:
        for c in mode_vals.columns:
            if c in X.columns and c not in numeric_cols:
                baseline[c] = mode_vals[c].iloc[0]

    if anchor_x is not None:
        baseline = baseline.copy()
        for c in X.columns:
            if c in anchor_x.index and pd.notna(anchor_x[c]):
                baseline[c] = anchor_x[c]

    # Ensure bounds exist for each numeric col (fallback to observed min/max)
    for c in numeric_cols:
        if c not in bounds:
            s = X[c].dropna()
            if s.empty:
                continue
            bounds[c] = Bounds(float(s.min()), float(s.max()))

    def predict(row: pd.Series) -> float:
        return float(pipe.predict(pd.DataFrame([row]))[0])

    def clip_row(row: pd.Series) -> pd.Series:
        out = row.copy()
        for c in numeric_cols:
            if c in bounds:
                out[c] = np.clip(out[c], bounds[c].low, bounds[c].high)
        return out

    x0 = clip_row(baseline).copy()

    def obj(vec: np.ndarray) -> float:
        row = x0.copy()
        # map vec to numeric columns
        for i, c in enumerate(numeric_cols):
            row[c] = vec[i]
        row = clip_row(row)
        yhat = predict(row)
        penalty = step_penalty * float(np.sum((vec - baseline[numeric_cols].values)**2))
        return abs(yhat) + penalty

    # Initialization grid
    best_vec = x0[numeric_cols].values.astype(float)
    best_score = obj(best_vec)

    inits = [best_vec]
    for _ in range(n_starts - 1):
        rand = []
        for c in numeric_cols:
            b = bounds[c]
            rand.append(rng.uniform(b.low, b.high))
        inits.append(np.array(rand, dtype=float))

    for init in inits:
        if SCIPY_OK:
            res = minimize(obj, init, method="Nelder-Mead", options={"maxiter": 200, "xatol": 1e-3, "fatol": 1e-4})
            vec = res.x if res.success else init
        else:
            # Fallback: small coordinate descent
            vec = init.copy()
            for _ in range(60):
                for i, c in enumerate(numeric_cols):
                    current = vec[i]
                    for delta in [0.05, -0.05, 0.02, -0.02]:
                        trial = vec.copy()
                        width = (bounds[c].high - bounds[c].low) or 1.0
                        trial[i] = np.clip(current + delta * width, bounds[c].low, bounds[c].high)
                        if obj(trial) < obj(vec):
                            vec = trial

        score = obj(vec)
        if score < best_score:
            best_score = score
            best_vec = vec

    # Build final recommended row
    rec = x0.copy()
    for i, c in enumerate(numeric_cols):
        rec[c] = best_vec[i]
    rec = clip_row(rec)
    pred = predict(rec)
    return rec, float(pred)


def main():
    print("Loading data...")
    df = load_data(CSV_PATH)

    print("Splitting columns...")
    numeric_cols, categorical_cols = split_columns(df)
    categorical_cols+=['stack_part_num',
                        'capacity',
                        'mba_part_num',
                        'pre2_date',
                        'clrm_exit_date',
                        'fsa_part_num_0',
                        'line_num','pivot_vendor','hsa_eblock_site','hsa_fw','motor_date_code','media_config','fcw_adate','disc',
                        'spacer_type',  'crx_trans_seq',
                        'ramp_date_code',
                        'ramp_mold_num',
                        'ramp_cavity_num']
    numeric_cols=[ i for i in numeric_cols if i not in ['stack_part_num',
        'capacity',
        'mba_part_num',
        'pre2_date',
        'clrm_exit_date',
        'fsa_part_num_0',
        'line_num','pivot_vendor','hsa_eblock_site','hsa_fw','motor_date_code','media_config','fcw_adate','disc',
        'spacer_type',  'crx_trans_seq',
        'ramp_date_code',
        'ramp_mold_num',
        'ramp_cavity_num'   ]]
    print(f"Numeric: {len(numeric_cols)} | Categorical: {len(categorical_cols)}")

    print("Running EDA...")
    eda(df, numeric_cols)

    print("Preparing features...")
    X = df.drop(columns=[TARGET])
    y = df[TARGET].astype(float)

    pre = build_preprocessor(numeric_cols, categorical_cols)

    print("Cross-validating models...")
    cv_res = evaluate_models(X, y, pre)
    print("CV results:\n", cv_res)

    print("Fitting best model...")
    best_name, pipe = fit_best_model(X, y, pre, cv_res)
    print(f"Best model: {best_name}")

    print("Computing feature importance...")
    imp_df = feature_importance(pipe, X, y)
    top_imp = imp_df.head(25)
    top_imp.to_csv(OUT_DIR / "top_factors.csv", index=False)

    # Save a simple text summary
    with open(OUT_DIR / "SUMMARY.txt", "w", encoding="utf-8") as f:
        f.write("=== dtrk Modeling Summary ===\n")
        f.write(f"Best model: {best_name}\n")
        f.write("\nTop important factors (permutation):\n")
        f.write(top_imp.to_string(index=False))
        f.write("\n")

    # Optimization to drive prediction near 0 (only over numeric factors)
    print("Estimating bounds for optimization...")
    bnds = estimate_bounds(df, numeric_cols)

    print("Optimizing factor values to push dtrk -> 0...")
    rec, pred = suggest_to_zero(pipe, X, numeric_cols, bnds, anchor_x=None, n_starts=25)
    rec_df = rec.to_frame(name="recommended_value")
    rec_df.to_csv(OUT_DIR / "recommended_factor_settings.csv")

    with open(OUT_DIR / "SUMMARY.txt", "a", encoding="utf-8") as f:
        f.write("\n=== Control Suggestion ===\n")
        f.write("Goal: make model prediction close to 0.\n")
        f.write(f"Predicted dtrk at recommendation: {pred:.6f}\n")
        f.write("Recommended numeric factors:\n")
        f.write(rec_df.loc[rec_df.index.isin(numeric_cols)].to_string())

    print("Done. See the 'outputs' folder for all artifacts.")

if __name__ == "__main__":
    t0 = time.time()
    try:
        main()
    except Exception as e:
        print("ERROR:", str(e))
    finally:
        print(f"Elapsed: {time.time() - t0:.2f}s")
