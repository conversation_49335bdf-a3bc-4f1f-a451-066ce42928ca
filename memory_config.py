"""
Memory optimization configuration for large dataset processing
"""

import os
import psutil
from typing import Dict, Any

def get_system_info() -> Dict[str, Any]:
    """Get system memory and CPU information"""
    memory = psutil.virtual_memory()
    cpu_count = psutil.cpu_count()
    
    return {
        'total_memory_gb': memory.total / (1024**3),
        'available_memory_gb': memory.available / (1024**3),
        'cpu_count': cpu_count,
        'memory_percent': memory.percent
    }

def get_optimal_settings(file_size_gb: float) -> Dict[str, Any]:
    """Get optimal processing settings based on file size and system resources"""
    system_info = get_system_info()
    available_memory = system_info['available_memory_gb']
    
    # Conservative memory usage (use max 70% of available memory)
    max_memory_usage = available_memory * 0.7
    
    # Estimate memory needed (rough approximation: CSV file uses 3-5x its size in memory when loaded)
    estimated_memory_needed = file_size_gb * 4
    
    settings = {
        'use_chunked_processing': estimated_memory_needed > max_memory_usage,
        'chunk_size': 50000,  # Default chunk size
        'max_memory_gb': max_memory_usage,
        'use_sampling': file_size_gb > 2.0,
        'sample_size_for_cv': 100000,
        'sample_size_for_eda': 200000,
        'max_correlation_features': 50,
        'n_jobs': min(system_info['cpu_count'], 4)  # Limit parallel jobs
    }
    
    # Adjust chunk size based on available memory
    if available_memory < 4:
        settings['chunk_size'] = 25000
    elif available_memory > 16:
        settings['chunk_size'] = 100000
    
    # Adjust sampling for very large files
    if file_size_gb > 5:
        settings['sample_size_for_cv'] = 50000
        settings['sample_size_for_eda'] = 100000
    
    return settings

def print_memory_recommendations(file_size_gb: float):
    """Print memory optimization recommendations"""
    system_info = get_system_info()
    settings = get_optimal_settings(file_size_gb)
    
    print("=== Memory Optimization Analysis ===")
    print(f"File size: {file_size_gb:.2f} GB")
    print(f"System memory: {system_info['total_memory_gb']:.1f} GB total, {system_info['available_memory_gb']:.1f} GB available")
    print(f"CPU cores: {system_info['cpu_count']}")
    print(f"Current memory usage: {system_info['memory_percent']:.1f}%")
    
    print("\n=== Recommended Settings ===")
    print(f"Use chunked processing: {settings['use_chunked_processing']}")
    print(f"Chunk size: {settings['chunk_size']:,}")
    print(f"Use sampling for CV: {settings['use_sampling']}")
    print(f"CV sample size: {settings['sample_size_for_cv']:,}")
    print(f"EDA sample size: {settings['sample_size_for_eda']:,}")
    print(f"Max correlation features: {settings['max_correlation_features']}")
    print(f"Parallel jobs: {settings['n_jobs']}")
    
    if settings['use_chunked_processing']:
        print("\n⚠️  Large file detected - using memory-optimized processing")
    
    if system_info['available_memory_gb'] < 8:
        print("\n⚠️  Low memory system - consider closing other applications")
    
    estimated_peak_memory = file_size_gb * 3
    if estimated_peak_memory > system_info['available_memory_gb']:
        print(f"\n⚠️  Warning: Estimated peak memory usage ({estimated_peak_memory:.1f} GB) may exceed available memory")
        print("   Consider processing on a machine with more RAM or using a smaller sample")

def set_pandas_options():
    """Set pandas options for memory efficiency"""
    import pandas as pd
    
    # Reduce memory usage
    pd.set_option('mode.copy_on_write', True)
    
    # Limit display to avoid memory issues with large DataFrames
    pd.set_option('display.max_rows', 100)
    pd.set_option('display.max_columns', 50)

def monitor_memory_usage(func):
    """Decorator to monitor memory usage of functions"""
    def wrapper(*args, **kwargs):
        import gc
        gc.collect()
        
        memory_before = psutil.Process().memory_info().rss / (1024**3)
        result = func(*args, **kwargs)
        memory_after = psutil.Process().memory_info().rss / (1024**3)
        
        print(f"Memory usage for {func.__name__}: {memory_before:.2f} GB -> {memory_after:.2f} GB (Δ{memory_after-memory_before:+.2f} GB)")
        return result
    return wrapper
