#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
T285 DTRK参数时间序列分析工具
用于分析不同CS1_ID组的DTRK参数变化，特别关注2026-02的shift现象
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os
import warnings
warnings.filterwarnings('ignore')
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.metrics import mean_squared_error, r2_score

# 使用标准字体，避免中文乱码问题
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def load_data(file_path):
    """
    加载CSV数据文件
    """
    print(f"正在加载数据: {file_path}")
    df = pd.read_csv(file_path)
    print(f"数据加载完成，共 {len(df)} 行")
    return df

def preprocess_data(df):
    """
    数据预处理
    """
    print("正在进行数据预处理...")
    
    # 不转换日期格式，直接使用字符串形式的日期
    # 确保scopy_st_week列存在
    if 'scopy_st_week' not in df.columns:
        print("警告: 数据中不存在'scopy_st_week'列")
        return df
    
    # 确保DTRK参数为数值类型
    df['p285pre2_dtrk'] = pd.to_numeric(df['p285pre2_dtrk'], errors='coerce')
    
    # 删除DTRK参数为空的行
    df = df.dropna(subset=['p285pre2_dtrk'])
    
    # 提取CS1_ID的前几位作为分组依据（可根据实际情况调整）
    if 'cs1_id' in df.columns:
        df['cs1_group'] = df['cs1_id'].str[:4]
    else:
        print("警告: 数据中不存在'cs1_id'列")
    
    print(f"数据预处理完成，剩余 {len(df)} 行")
    return df

def analyze_by_time(df):
    """
    按时间分析DTRK参数变化
    """
    print("正在进行时间序列分析...")
    
    # 按周和CS1组分组计算DTRK参数的均值
    time_analysis = df.groupby(['scopy_st_week', 'cs1_group'])['p285pre2_dtrk'].agg(['mean', 'std', 'count']).reset_index()
    
    # 透视表以便于可视化
    pivot_table = time_analysis.pivot(index='scopy_st_week', columns='cs1_group', values='mean')
    
    # 计算总体均值
    overall_mean = df.groupby('scopy_st_week')['p285pre2_dtrk'].mean().reset_index()
    
    return time_analysis, pivot_table, overall_mean

def analyze_shift_period(df, target_period='2026-02'):
    """
    分析特定时间段的shift现象
    """
    print(f"正在分析 {target_period} 时间段的shift现象...")
    
    # 筛选目标时间段的数据
    shift_data = df[df['scopy_st_week'] == target_period]
    
    # 计算各CS1组在此时间段的DTRK参数统计量
    shift_stats = shift_data.groupby('cs1_group')['p285pre2_dtrk'].agg(['mean', 'std', 'count', 'min', 'max']).reset_index()
    
    # 计算前后时间段的对比数据
    # 获取所有唯一的时间点并排序
    all_periods = sorted(df['scopy_st_week'].unique())
    
    # 找到目标时间段的索引
    if target_period in all_periods:
        target_idx = all_periods.index(target_period)
        
        # 确定前后时间段
        before_period = all_periods[target_idx-1] if target_idx > 0 else None
        after_period = all_periods[target_idx+1] if target_idx < len(all_periods)-1 else None
        
        # 获取前后时间段的数据
        before_data = df[df['scopy_st_week'] == before_period] if before_period else None
        after_data = df[df['scopy_st_week'] == after_period] if after_period else None
        
        # 计算前后时间段的统计量
        if before_data is not None:
            before_stats = before_data.groupby('cs1_group')['p285pre2_dtrk'].mean().reset_index()
            before_stats.columns = ['cs1_group', 'before_mean']
        else:
            before_stats = None
            
        if after_data is not None:
            after_stats = after_data.groupby('cs1_group')['p285pre2_dtrk'].mean().reset_index()
            after_stats.columns = ['cs1_group', 'after_mean']
        else:
            after_stats = None
        
        # 合并统计结果
        if before_stats is not None:
            shift_stats = pd.merge(shift_stats, before_stats, on='cs1_group', how='left')
        
        if after_stats is not None:
            shift_stats = pd.merge(shift_stats, after_stats, on='cs1_group', how='left')
        
        # 计算变化量
        if before_stats is not None:
            shift_stats['change_from_before'] = shift_stats['mean'] - shift_stats['before_mean']
        
        if after_stats is not None:
            shift_stats['change_to_after'] = shift_stats['after_mean'] - shift_stats['mean']
    
    return shift_stats, shift_data

def visualize_time_series(pivot_table, overall_mean, output_dir):
    """
    Visualize time series data
    """
    print("Generating time series visualization charts...")
    
    plt.figure(figsize=(14, 8))
    
    # Plot time series for each CS1 group
    for column in pivot_table.columns:
        plt.plot(pivot_table.index, pivot_table[column], label=f'CS1 Group: {column}', alpha=0.7)
    
    # Plot overall mean
    plt.plot(overall_mean['scopy_st_week'], overall_mean['p285pre2_dtrk'], 
             'k--', linewidth=2, label='Overall Mean')
    
    # Add reference line
    plt.axhline(y=0, color='gray', linestyle='-', alpha=0.3)
    
    # Mark 2026-02 time point
    if '2026-02' in overall_mean['scopy_st_week'].values:
        plt.axvline(x='2026-02', color='red', linestyle='--', alpha=0.5, 
                   label='2026-02 Shift Point')
    
    plt.title('DTRK Parameter Time Series by CS1_ID Group', fontsize=16)
    plt.xlabel('Time (Week)', fontsize=14)
    plt.ylabel('DTRK Parameter Mean', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.legend(loc='best')
    plt.tight_layout()
    
    # Save chart
    output_path = os.path.join(output_dir, 'dtrk_time_series.png')
    plt.savefig(output_path, dpi=300)
    print(f"Time series chart saved to: {output_path}")
    
    plt.close()

def visualize_shift_analysis(shift_stats, output_dir):
    """
    Visualize shift analysis results
    """
    print("Generating shift analysis visualization charts...")
    
    # 1. Compare DTRK means for each CS1 group during shift period
    plt.figure(figsize=(12, 6))
    
    # Sort by mean
    sorted_data = shift_stats.sort_values('mean')
    
    # Create bar chart
    bars = plt.bar(sorted_data['cs1_group'], sorted_data['mean'], yerr=sorted_data['std'],
                  alpha=0.7, capsize=5)
    
    # Add value labels to bars
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height,
                f'{height:.2f}', ha='center', va='bottom', rotation=0)
    
    plt.title('DTRK Parameter Means by CS1 Group during 2026-02', fontsize=16)
    plt.xlabel('CS1 Group', fontsize=14)
    plt.ylabel('DTRK Parameter Mean', fontsize=14)
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    
    # Save chart
    output_path = os.path.join(output_dir, 'dtrk_shift_means.png')
    plt.savefig(output_path, dpi=300)
    print(f"Shift period means comparison chart saved to: {output_path}")
    
    plt.close()
    
    # 2. If comparison data with previous period exists, create change chart
    if 'change_from_before' in shift_stats.columns:
        plt.figure(figsize=(12, 6))
        
        # Sort by change amount
        sorted_data = shift_stats.sort_values('change_from_before')
        
        # Create bar chart
        bars = plt.bar(sorted_data['cs1_group'], sorted_data['change_from_before'],
                      alpha=0.7)
        
        # Add value labels to bars
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.2f}', ha='center', va='bottom', rotation=0)
        
        plt.title('DTRK Parameter Changes by CS1 Group (2026-02 vs Previous Period)', fontsize=16)
        plt.xlabel('CS1 Group', fontsize=14)
        plt.ylabel('DTRK Parameter Change', fontsize=14)
        plt.grid(True, alpha=0.3, axis='y')
        plt.tight_layout()
        
        # Save chart
        output_path = os.path.join(output_dir, 'dtrk_shift_changes.png')
        plt.savefig(output_path, dpi=300)
        print(f"Shift period changes chart saved to: {output_path}")
        
        plt.close()

def analyze_component_correlation(df, shift_data, output_dir):
    """
    Analyze component correlation to identify potential causes of the shift
    """
    print("Analyzing component correlations...")
    
    # Select potential component columns (adjust based on data structure)
    potential_components = [
        'drive_part_num', 'drive_sbr_num', 'cms_config', 'stack_part_num',
        'hga_supplier', 'motor_vend_id', 'disc_install_id', 'tester_id',
        'interface', 'product', 'capacity'
    ]
    
    # Filter for component columns that exist in the data
    available_components = [col for col in potential_components if col in df.columns]
    
    # Create results directory
    component_dir = os.path.join(output_dir, 'component_analysis')
    os.makedirs(component_dir, exist_ok=True)
    
    # Analyze relationship between each component and DTRK parameter
    component_results = {}
    
    for component in available_components:
        # Calculate DTRK parameter statistics for each component value during shift period
        component_stats = shift_data.groupby(component)['p285pre2_dtrk'].agg(['mean', 'std', 'count']).reset_index()
        component_stats = component_stats.sort_values('mean')
        
        # Only keep groups with sufficient samples
        component_stats = component_stats[component_stats['count'] >= 5]
        
        if len(component_stats) > 1:  # Ensure there are enough groups to compare
            # Visualization
            plt.figure(figsize=(12, 6))
            
            # Create bar chart
            bars = plt.bar(component_stats[component].astype(str), component_stats['mean'], 
                          yerr=component_stats['std'], alpha=0.7, capsize=5)
            
            plt.title(f'DTRK Parameter Means by {component} during 2026-02', fontsize=16)
            plt.xlabel(component, fontsize=14)
            plt.ylabel('DTRK Parameter Mean', fontsize=14)
            plt.xticks(rotation=45, ha='right')
            plt.grid(True, alpha=0.3, axis='y')
            plt.tight_layout()
            
            # Save chart
            output_path = os.path.join(component_dir, f'dtrk_{component}_analysis.png')
            plt.savefig(output_path, dpi=300)
            
            plt.close()
            
            # Save statistics results
            component_results[component] = component_stats
    
    return component_results

def main():
    # 设置文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_file = os.path.join(current_dir, 'T285.csv')
    output_dir = os.path.join(current_dir, 'analysis_results')
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载数据
    df = load_data(data_file)
    
    # 数据预处理
    df = preprocess_data(df)
    
    # 时间序列分析
    time_analysis, pivot_table, overall_mean = analyze_by_time(df)
    
    # 可视化时间序列
    visualize_time_series(pivot_table, overall_mean, output_dir)
    
    # 分析2026-02的shift现象
    shift_stats, shift_data = analyze_shift_period(df, target_period='2026-02')
    
    # 可视化shift分析结果
    visualize_shift_analysis(shift_stats, output_dir)
    
    # 分析组件相关性
    component_results = analyze_component_correlation(df, shift_data, output_dir)
    
    # 输出分析报告
    report_path = os.path.join(output_dir, 'analysis_report.txt')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("# T285 DTRK参数Shift分析报告\n\n")
        f.write(f"分析日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## 1. 总体情况\n")
        f.write(f"- 数据总行数: {len(df)}\n")
        f.write(f"- 分析时间范围: {df['scopy_st_week'].min()} 至 {df['scopy_st_week'].max()}\n")
        f.write(f"- CS1_ID组数量: {df['cs1_group'].nunique()}\n\n")
        
        f.write("## 2. 2026-02 Shift分析\n")
        f.write(f"- 2026-02期间数据行数: {len(shift_data)}\n")
        f.write(f"- 2026-02期间DTRK参数均值: {shift_data['p285pre2_dtrk'].mean():.2f}\n")
        f.write(f"- 2026-02期间DTRK参数标准差: {shift_data['p285pre2_dtrk'].std():.2f}\n\n")
        
        f.write("### 各CS1组在2026-02期间的DTRK参数统计\n")
        f.write(shift_stats.to_string(index=False))
        f.write("\n\n")
        
        if 'change_from_before' in shift_stats.columns:
            # 找出变化最大的CS1组
            max_decrease = shift_stats.loc[shift_stats['change_from_before'].idxmin()]
            
            f.write("### 变化最显著的CS1组\n")
            f.write(f"- CS1组: {max_decrease['cs1_group']}\n")
            f.write(f"- 变化量: {max_decrease['change_from_before']:.2f}\n")
            f.write(f"- 2026-02期间均值: {max_decrease['mean']:.2f}\n")
            f.write(f"- 前一周期均值: {max_decrease['before_mean']:.2f}\n\n")
        
        f.write("## 3. 可能的原因分析\n")
        f.write("根据组件分析，以下组件可能与2026-02的shift现象相关:\n\n")
        
        for component, stats in component_results.items():
            # 计算组件值之间的最大差异
            if len(stats) > 1:
                max_diff = stats['mean'].max() - stats['mean'].min()
                max_component = stats.loc[stats['mean'].idxmax()][component]
                min_component = stats.loc[stats['mean'].idxmin()][component]
                
                f.write(f"### {component}\n")
                f.write(f"- 最大差异: {max_diff:.2f}\n")
                f.write(f"- 最高值组件: {max_component} (均值: {stats['mean'].max():.2f})\n")
                f.write(f"- 最低值组件: {min_component} (均值: {stats['mean'].min():.2f})\n\n")
        
        f.write("## 4. 结论与建议\n")
        f.write("根据分析结果，2026-02期间的DTRK参数shift现象可能与以下因素有关:\n\n")
        f.write("1. 请检查在此期间是否有组件配置变更\n")
        f.write("2. 特别关注CS1_ID分组间的差异，尤其是变化最显著的组\n")
        f.write("3. 建议进一步分析组件相关性，特别是与DTRK参数相关性较高的组件\n")
        f.write("4. 考虑检查生产流程中可能导致此类shift的环节\n\n")
        
        f.write("分析结果图表已保存在analysis_results目录下。\n")
    
    print(f"分析报告已生成: {report_path}")
    print("分析完成！")

if __name__ == "__main__":
    main()